# ===============================================================================
# Fine-Gray竞争风险建模脚本 - 论文版本
#
# 研究目的：构建Fine-Gray竞争风险模型，预测10年心血管死亡风险
# 分析策略：基于LASSO筛选的变量构建最终预测模型
#
# 输入：D:/FG/EXCEL/R-LASSO.xlsx (LASSO建模数据集)
# 输出：D:/FG/F-G建模/ (最终模型结果)
#
# 建模变量：Age, T90, AHI, Packyears (4个变量)
# 结局事件：心血管死亡 (vital=1)
# 竞争事件：其他原因死亡 (vital=2)
# 删失事件：存活 (vital=0)
#
# 方法学要点：
# - Fine-Gray模型处理竞争风险，估计亚分布风险比
# - 三分位数风险分层确保样本量均衡和统计功效
# - 累积发病率函数评估10年预测性能
#
# 作者：数据科学团队
# 日期：2025-08-05
# 版本：v5.0 (论文发表版)
# ===============================================================================

# 清理环境
rm(list = ls())
gc()

cat("===============================================================================\n")
cat("              Fine-Gray竞争风险建模：10年心血管死亡风险预测                \n")
cat("===============================================================================\n")
cat("分析目标: 构建Fine-Gray竞争风险预测模型，评估10年心血管死亡风险\n")
cat("建模变量: Age, T90, AHI, Packyears (LASSO筛选的核心变量)\n")
cat("风险分层: 基于三分位数的风险分层策略\n")
cat("开始时间:", format(Sys.time(), "%Y-%m-%d %H:%M:%S"), "\n\n")

# ===============================================================================
# 1. 加载必需的R包 (全部为通用开源包)
# ===============================================================================

cat("=== 第一步：加载R包 ===\n")

# 定义所需的开源R包
required_packages <- c(
  "readxl",        # 读取Excel文件 (开源)
  "openxlsx",      # 写入Excel文件 (开源)
  "cmprsk",        # 竞争风险分析 (开源，Fine-Gray模型标准包)
  "survival",      # 生存分析 (开源，R基础包)
  "dplyr",         # 数据处理 (开源)
  "tidyr",         # 数据整理 (开源)
  "ggplot2",       # 数据可视化 (开源)
  "gridExtra",     # 图形排列 (开源)
  "knitr",         # 表格美化 (开源)
  "RColorBrewer"   # 颜色方案 (开源)
)

# 检查并安装缺失的包
cat("正在检查和安装必需的R包...\n")
missing_packages <- setdiff(required_packages, rownames(installed.packages()))
if (length(missing_packages) > 0) {
  cat("正在安装缺失的R包:", paste(missing_packages, collapse = ", "), "\n")
  install.packages(missing_packages, dependencies = TRUE)
}

# 加载所有包
for (pkg in required_packages) {
  if (!require(pkg, character.only = TRUE)) {
    stop("无法加载R包: ", pkg)
  }
}

cat("✓ 所有R包加载成功\n\n")

# ===============================================================================
# 2. 数据读取和验证
# ===============================================================================

cat("=== 第二步：数据读取和验证 ===\n")

# 设置文件路径 (使用指定路径)
input_file <- "D:/FG/EXCEL/R-LASSO.xlsx"  # 使用指定的LASSO数据集路径
output_dir <- "D:/FG/F-G建模"              # 使用指定的输出目录

# 检查输入文件是否存在
if (!file.exists(input_file)) {
  stop("错误：找不到输入文件：", input_file)
}

cat("正在读取LASSO建模数据集...\n")
cat("文件路径:", input_file, "\n")

# 读取数据 (尝试读取第一个工作表)
tryCatch({
  # 先尝试读取"LASSO建模数据集"工作表，如果不存在则读取第一个工作表
  sheet_names <- excel_sheets(input_file)
  if ("LASSO建模数据集" %in% sheet_names) {
    train_data <- read_excel(input_file, sheet = "LASSO建模数据集")
    cat("✓ 从'LASSO建模数据集'工作表读取数据成功\n")
  } else {
    train_data <- read_excel(input_file, sheet = 1)
    cat("✓ 从第一个工作表读取数据成功\n")
  }
}, error = function(e) {
  stop("读取数据时出错：", e$message)
})

# 基本数据信息
cat("\n--- 数据基本信息 ---\n")
cat("数据维度:", nrow(train_data), "行 ×", ncol(train_data), "列\n")
cat("列名:", paste(names(train_data), collapse = ", "), "\n")

# 检查关键列是否存在 (适配新数据结构)
required_columns <- c("ID", "vital", "time")
missing_columns <- required_columns[!required_columns %in% names(train_data)]

if (length(missing_columns) > 0) {
  stop("错误：缺少必需的列：", paste(missing_columns, collapse = ", "))
}

cat("✓ 所有必需的基础列都存在\n")

# 验证事件编码
valid_events <- c(0, 1, 2)
if (!all(train_data$vital %in% valid_events)) {
  stop("事件编码错误，应为0(删失)、1(主要事件)、2(竞争事件)")
}
cat("✓ 事件编码验证通过\n")

# 验证时间变量
if (!is.numeric(train_data$time) || any(train_data$time <= 0, na.rm = TRUE)) {
  stop("时间变量应为正数")
}
cat("✓ 时间变量验证通过\n")

# 识别自变量 (除了ID, vital, time之外的所有列)
base_columns <- c("ID", "vital", "time")
selected_vars <- setdiff(names(train_data), base_columns)

cat("\n--- 变量识别结果 ---\n")
cat("基础列数量:", length(base_columns), "个 (ID, vital, time)\n")
cat("自变量数量:", length(selected_vars), "个\n")
cat("自变量列表:", paste(selected_vars, collapse = ", "), "\n")

# 验证变量名是否符合预期（基于LASSO筛选结果）
expected_vars <- c("Age", "T90", "AHI", "Packyears")
if (length(selected_vars) != 4) {
  cat("⚠️ 警告：检测到", length(selected_vars), "个变量，预期为4个变量\n")
}

# 检查是否包含预期的核心变量
missing_expected <- expected_vars[!expected_vars %in% selected_vars]
if (length(missing_expected) > 0) {
  cat("⚠️ 警告：以下预期变量未找到:", paste(missing_expected, collapse = ", "), "\n")
}

extra_vars <- selected_vars[!selected_vars %in% expected_vars]
if (length(extra_vars) > 0) {
  cat("ℹ️ 信息：检测到额外变量:", paste(extra_vars, collapse = ", "), "\n")
}

# 准备建模数据 (适配新数据结构)
model_data <- train_data %>%
  select(ID, vital, time, all_of(selected_vars)) %>%
  filter(!is.na(vital) & !is.na(time))

cat("\n--- 建模数据准备 ---\n")
cat("原始样本量:", nrow(train_data), "\n")
cat("建模数据样本量:", nrow(model_data), "\n")

# 检查事件分布
cat("\n--- 事件分布检查 ---\n")
event_table <- table(model_data$vital)
cat("事件分布:\n")
cat("  删失(0):", event_table["0"], "例 (", round(event_table["0"]/nrow(model_data)*100, 1), "%)\n")
cat("  主要事件(1):", event_table["1"], "例 (", round(event_table["1"]/nrow(model_data)*100, 1), "%)\n")
cat("  竞争事件(2):", event_table["2"], "例 (", round(event_table["2"]/nrow(model_data)*100, 1), "%)\n")

# ===============================================================================
# 3. 变量类型识别和数据质量检查
# ===============================================================================

cat("\n=== 第三步：变量类型识别和数据质量检查 ===\n")

# 变量类型识别函数
identify_variable_type <- function(x) {
  x_clean <- x[!is.na(x)]
  if (length(x_clean) == 0) return("unknown")

  unique_vals <- unique(x_clean)
  # 判断是否为二分类变量 (只有0和1)
  if (length(unique_vals) <= 2 && all(unique_vals %in% c(0, 1))) {
    return("binary")
  } else if (is.numeric(x_clean)) {
    return("continuous")
  } else {
    return("categorical")
  }
}

# 对自变量进行类型分类
cat("正在识别变量类型...\n")
var_types <- sapply(model_data[selected_vars], identify_variable_type)
binary_vars <- names(var_types)[var_types == "binary"]
continuous_vars <- names(var_types)[var_types == "continuous"]
categorical_vars <- names(var_types)[var_types == "categorical"]
unknown_vars <- names(var_types)[var_types == "unknown"]

cat("变量类型分布:\n")
cat("  二分类变量:", length(binary_vars), "个\n")
if (length(binary_vars) > 0) {
  cat("    ", paste(binary_vars, collapse = ", "), "\n")
}

cat("  连续变量:", length(continuous_vars), "个 (已标准化)\n")
if (length(continuous_vars) > 0) {
  cat("    ", paste(continuous_vars, collapse = ", "), "\n")
}

if (length(categorical_vars) > 0) {
  cat("  分类变量:", length(categorical_vars), "个\n")
  cat("    ", paste(categorical_vars, collapse = ", "), "\n")
}

if (length(unknown_vars) > 0) {
  cat("  未知类型变量:", length(unknown_vars), "个\n")
  cat("    ", paste(unknown_vars, collapse = ", "), "\n")
  warning("发现未知类型变量，请检查数据")
}

# 检查数据完整性
cat("\n--- 数据完整性检查 ---\n")
predictor_data <- model_data[, selected_vars, drop = FALSE]
missing_summary <- sapply(predictor_data, function(x) sum(is.na(x)))

if (any(missing_summary > 0)) {
  cat("⚠️ 发现缺失值:\n")
  missing_vars <- missing_summary[missing_summary > 0]
  for (var in names(missing_vars)) {
    missing_pct <- round(missing_vars[var] / nrow(model_data) * 100, 2)
    cat(sprintf("  %-12s: %d个缺失值 (%.2f%%)\n", var, missing_vars[var], missing_pct))
  }

  # 如果有缺失值，使用完整案例进行分析
  complete_cases <- complete.cases(predictor_data)
  if (sum(complete_cases) < nrow(predictor_data)) {
    cat("将使用完整案例进行建模 (", sum(complete_cases), "/", nrow(predictor_data), ")\n")
    model_data <- model_data[complete_cases, ]
    predictor_data <- predictor_data[complete_cases, ]
  }
} else {
  cat("✓ 自变量无缺失值\n")
}

# ===============================================================================
# 4. Fine-Gray模型构建
# ===============================================================================

cat("\n=== 第四步：Fine-Gray模型构建 ===\n")

cat("正在构建Fine-Gray竞争风险模型...\n")
cat("模型说明:\n")
cat("  - 使用cmprsk包的crr()函数\n")
cat("  - Fine-Gray模型是竞争风险分析的金标准\n")
cat("  - 自动处理竞争事件的IPCW权重\n")
cat("  - 估计亚分布风险比(sHR)\n\n")

# 准备协变量矩阵
cov_matrix <- as.matrix(model_data[, selected_vars])

cat("建模数据最终检查:\n")
cat("  样本量:", nrow(model_data), "\n")
cat("  变量数:", length(selected_vars), "\n")
cat("  协变量矩阵维度:", nrow(cov_matrix), "×", ncol(cov_matrix), "\n")

# 检查协变量矩阵的数值稳定性
cat("\n--- 协变量矩阵质量检查 ---\n")
if (any(is.na(cov_matrix))) {
  cat("⚠️ 警告：协变量矩阵包含缺失值\n")
} else {
  cat("✓ 协变量矩阵无缺失值\n")
}

if (any(is.infinite(cov_matrix))) {
  cat("⚠️ 警告：协变量矩阵包含无穷值\n")
} else {
  cat("✓ 协变量矩阵无无穷值\n")
}

# 检查变量的变异性
var_summary <- apply(cov_matrix, 2, function(x) c(mean = mean(x), sd = sd(x), min = min(x), max = max(x)))
zero_var_cols <- which(var_summary["sd", ] == 0)
if (length(zero_var_cols) > 0) {
  cat("⚠️ 警告：以下变量无变异性:", paste(colnames(cov_matrix)[zero_var_cols], collapse = ", "), "\n")
} else {
  cat("✓ 所有变量均有变异性\n")
}

# 构建Fine-Gray模型
# 注意：Fine-Gray模型内部自动处理竞争事件的IPCW权重
tryCatch({
  fg_model <- crr(
    ftime = model_data$time,              # 时间变量 (使用新的列名)
    fstatus = model_data$vital,           # 事件状态 (0=删失, 1=主要事件, 2=竞争事件)
    cov1 = cov_matrix,                    # 协变量矩阵
    failcode = 1,                         # 主要事件编码
    cencode = 0,                          # 删失编码
    variance = TRUE                       # 计算方差
  )

  cat("✓ Fine-Gray模型构建成功\n")

  # 模型收敛检查
  if (fg_model$converged) {
    cat("✓ 模型成功收敛\n")
  } else {
    cat("⚠️ 模型未完全收敛，结果需谨慎解释\n")
    cat("  建议：检查数据质量、减少变量数量或增加样本量\n")
  }

  n_iter_temp <- if(is.null(fg_model$n.iter)) "未记录" else fg_model$n.iter
  cat("  迭代次数:", n_iter_temp, "\n")

  # 检查模型系数的合理性
  cat("\n--- 模型系数初步检查 ---\n")
  if (any(is.na(fg_model$coef))) {
    cat("⚠️ 警告：模型系数包含缺失值\n")
  } else {
    cat("✓ 模型系数无缺失值\n")
  }

  if (any(abs(fg_model$coef) > 10)) {
    large_coef_vars <- selected_vars[abs(fg_model$coef) > 10]
    cat("⚠️ 警告：以下变量系数过大(>10):", paste(large_coef_vars, collapse = ", "), "\n")
    cat("  建议：检查数据标准化或存在共线性问题\n")
  } else {
    cat("✓ 所有系数在合理范围内\n")
  }

}, error = function(e) {
  stop("Fine-Gray模型构建失败：", e$message)
})

# ===============================================================================
# 5. 模型结果提取和分析
# ===============================================================================

cat("\n=== 第五步：模型结果提取和分析 ===\n")

cat("正在提取Fine-Gray模型结果...\n")

# 创建结果表格
results_table <- data.frame(
  Variable = selected_vars,
  Coefficient = fg_model$coef,
  SE = sqrt(diag(fg_model$var)),
  sHR = exp(fg_model$coef),                    # 亚分布风险比
  sHR_lower = exp(fg_model$coef - 1.96 * sqrt(diag(fg_model$var))),
  sHR_upper = exp(fg_model$coef + 1.96 * sqrt(diag(fg_model$var))),
  Z_value = fg_model$coef / sqrt(diag(fg_model$var)),
  P_value = 2 * (1 - pnorm(abs(fg_model$coef / sqrt(diag(fg_model$var)))))
)

# 添加变量类型信息
results_table$Variable_Type <- sapply(results_table$Variable, function(x) {
  if (x %in% binary_vars) return("二分类")
  if (x %in% continuous_vars) return("连续")
  return("其他")
})

# 添加统计显著性标记
results_table$Significance <- ifelse(results_table$P_value < 0.001, "***",
                                    ifelse(results_table$P_value < 0.01, "**",
                                          ifelse(results_table$P_value < 0.05, "*",
                                                ifelse(results_table$P_value < 0.1, ".", ""))))

# 格式化P值
results_table$P_value_formatted <- ifelse(
  results_table$P_value < 0.001,
  "<0.001",
  sprintf("%.3f", results_table$P_value)
)

# 格式化sHR及95%CI
results_table$sHR_CI <- sprintf("%.3f (%.3f-%.3f)",
                                results_table$sHR,
                                results_table$sHR_lower,
                                results_table$sHR_upper)

# 按P值排序
results_table <- results_table[order(results_table$P_value), ]

# 添加变量的临床解释
variable_descriptions <- data.frame(
  Variable = c("Age", "T90", "AHI", "Packyears"),
  Clinical_Name = c("年龄", "血氧饱和度<90%时间百分比", "呼吸暂停低通气指数", "吸烟包年数"),
  Clinical_Category = c("人口学特征", "睡眠呼吸暂停指标", "睡眠呼吸暂停指标", "心血管风险因子"),
  Unit = c("岁", "%", "次/小时", "包·年"),
  Clinical_Significance = c(
    "年龄是心血管疾病的重要危险因素",
    "反映睡眠期间严重低氧血症的程度",
    "睡眠呼吸暂停严重程度的金标准指标",
    "吸烟暴露量的综合指标"
  ),
  stringsAsFactors = FALSE
)

# 为不在预定义列表中的变量创建默认描述
missing_vars <- selected_vars[!selected_vars %in% variable_descriptions$Variable]
if (length(missing_vars) > 0) {
  cat("为以下变量创建默认临床描述:", paste(missing_vars, collapse = ", "), "\n")

  default_descriptions <- data.frame(
    Variable = missing_vars,
    Clinical_Name = missing_vars,  # 使用变量名作为临床名称
    Clinical_Category = "其他变量",
    Unit = "标准化值",
    Clinical_Significance = "需要进一步确定临床意义",
    stringsAsFactors = FALSE
  )

  variable_descriptions <- rbind(variable_descriptions, default_descriptions)
}

# 将临床信息合并到结果表中
results_table <- merge(results_table, variable_descriptions, by = "Variable", all.x = TRUE, sort = FALSE)

# 重新按原始顺序排列（按P值排序）
results_table <- results_table[order(results_table$P_value), ]

# 检查合并后的列名
cat("合并后的列名:", paste(names(results_table), collapse = ", "), "\n")

# 重新排序列（只选择存在的列）
available_cols <- names(results_table)
desired_cols <- c("Variable", "Clinical_Name", "Clinical_Category", "Variable_Type",
                  "Coefficient", "SE", "sHR", "sHR_lower", "sHR_upper",
                  "Z_value", "P_value", "Significance", "Clinical_Significance")
final_cols <- desired_cols[desired_cols %in% available_cols]
results_table <- results_table[, final_cols]



cat("✓ 模型结果提取完成\n")
cat("最终结果表列名:", paste(names(results_table), collapse = ", "), "\n")
cat("结果表维度:", nrow(results_table), "行 ×", ncol(results_table), "列\n")

# 显示主要结果
cat("\n--- Fine-Gray模型主要结果 ---\n")

# 检查需要的列是否存在，如果不存在则使用备选方案
display_cols <- c("Variable", "sHR_CI", "P_value_formatted", "Significance")
display_names <- c("变量", "亚分布风险比 (95%CI)", "P值", "显著性")

# 如果临床信息列存在，则添加它们
if ("Clinical_Name" %in% names(results_table)) {
  display_cols <- c("Variable", "Clinical_Name", "Clinical_Category", "sHR_CI", "P_value_formatted", "Significance")
  display_names <- c("变量", "临床名称", "变量分类", "亚分布风险比 (95%CI)", "P值", "显著性")
} else if ("Variable_Type" %in% names(results_table)) {
  display_cols <- c("Variable", "Variable_Type", "sHR_CI", "P_value_formatted", "Significance")
  display_names <- c("变量", "变量类型", "亚分布风险比 (95%CI)", "P值", "显著性")
}

# 只选择存在的列
existing_cols <- display_cols[display_cols %in% names(results_table)]
corresponding_names <- display_names[1:length(existing_cols)]

results_display <- results_table[, existing_cols]
names(results_display) <- corresponding_names

# 使用kable美化表格显示
if (require(knitr, quietly = TRUE)) {
  print(kable(results_display,
              caption = "Fine-Gray竞争风险模型结果",
              align = c("l", "c", "c", "c", "c")))
} else {
  print(results_display)
}

# 统计显著变量
significant_vars <- results_table[results_table$P_value < 0.05, ]
cat("\n--- 统计显著性分析 ---\n")
cat("统计显著变量数 (P<0.05):", nrow(significant_vars), "/", nrow(results_table), "\n")
if (nrow(significant_vars) > 0) {
  cat("显著变量列表:", paste(significant_vars$Variable, collapse = ", "), "\n")

  cat("\n--- 统计显著变量的临床解释 ---\n")
  for (i in 1:nrow(significant_vars)) {
    var_name <- significant_vars$Variable[i]

    # 安全地获取临床名称
    clinical_name <- if ("Clinical_Name" %in% names(significant_vars)) {
      significant_vars$Clinical_Name[i]
    } else {
      var_name  # 如果没有临床名称，使用变量名
    }

    shr_value <- significant_vars$sHR[i]
    p_value <- significant_vars$P_value[i]

    direction <- ifelse(shr_value > 1, "增加", "降低")
    magnitude <- round(abs(shr_value - 1) * 100, 1)

    # 显示变量信息
    if (clinical_name != var_name) {
      cat(sprintf("• %s (%s):\n", clinical_name, var_name))
    } else {
      cat(sprintf("• %s:\n", var_name))
    }
    cat(sprintf("  - %s10年心血管死亡风险%.1f%%\n", direction, magnitude))
    cat(sprintf("  - 亚分布风险比: %.3f (P=%.3f)\n", shr_value, p_value))

    # 添加临床意义解释
    if (var_name == "Age") {
      cat("  - 临床意义: 年龄每增加1个标准差，心血管死亡风险相应变化\n")
    } else if (var_name == "T90") {
      cat("  - 临床意义: 严重低氧血症时间延长与心血管死亡风险相关\n")
    } else if (var_name == "AHI") {
      cat("  - 临床意义: 睡眠呼吸暂停严重程度与心血管预后相关\n")
    } else if (var_name == "Packyears") {
      cat("  - 临床意义: 吸烟暴露量与心血管死亡风险呈剂量-反应关系\n")
    }
    cat("\n")
  }
} else {
  cat("未发现统计显著的危险因素，可能原因:\n")
  cat("  - 样本量不足\n")
  cat("  - 变量效应较小\n")
  cat("  - 需要更长的随访时间\n")
}

# ===============================================================================
# 6. 创建输出目录和保存结果
# ===============================================================================

cat("\n=== 第六步：创建输出目录和保存结果 ===\n")

# 创建输出目录
if (!dir.exists(output_dir)) {
  dir.create(output_dir, recursive = TRUE)
  cat("✓ 创建输出目录:", output_dir, "\n")
} else {
  cat("✓ 输出目录已存在:", output_dir, "\n")
}

# 创建输出文件路径
fg_output_file <- file.path(output_dir, "Fine-Gray建模结果.xlsx")
fg_results_file <- file.path(output_dir, "Fine-Gray建模完整结果.rds")

cat("正在保存Fine-Gray建模结果...\n")

# ===============================================================================
# 7. 模型诊断和统计
# ===============================================================================

cat("\n=== 第七步：模型诊断和统计 ===\n")

# 计算统计量
n_events <- sum(model_data$vital == 1)
n_competing <- sum(model_data$vital == 2)
n_censored <- sum(model_data$vital == 0)
n_total <- nrow(model_data)

cat("--- 样本统计 ---\n")
cat("总样本量:", n_total, "\n")
cat("主要事件数:", n_events, "例 (", round(n_events/n_total * 100, 1), "%)\n")
cat("竞争事件数:", n_competing, "例 (", round(n_competing/n_total * 100, 1), "%)\n")
cat("删失数:", n_censored, "例 (", round(n_censored/n_total * 100, 1), "%)\n")

# EPV计算
epv <- n_events / length(selected_vars)
cat("\n--- 模型质量评估 ---\n")
cat("事件/变量比 (EPV):", round(epv, 1), ":1\n")

if (epv < 5) {
  cat("⚠️ EPV < 5，模型存在过拟合高风险\n")
  epv_quality <- "高风险"
} else if (epv < 10) {
  cat("⚠️ EPV < 10，模型可能存在过拟合风险\n")
  epv_quality <- "中等风险"
} else if (epv >= 20) {
  cat("✓ EPV ≥ 20，模型统计功效优秀\n")
  epv_quality <- "优秀"
} else {
  cat("✓ EPV在10-20之间，模型统计功效良好\n")
  epv_quality <- "良好"
}

cat("模型收敛状态:", ifelse(fg_model$converged, "✓ 成功收敛", "⚠️ 未完全收敛"), "\n")
n_iter <- if(is.null(fg_model$n.iter)) "未记录" else fg_model$n.iter
cat("迭代次数:", n_iter, "\n")

# 创建模型诊断摘要
model_diagnostics <- data.frame(
  项目 = c("总样本量", "主要事件数", "竞争事件数", "删失数", "主要事件率(%)",
          "EPV比例", "EPV质量评估", "模型收敛", "迭代次数", "变量数量"),
  结果 = c(as.character(n_total),
          as.character(n_events),
          as.character(n_competing),
          as.character(n_censored),
          as.character(round(n_events/n_total * 100, 1)),
          paste0(round(epv, 1), ":1"),
          epv_quality,
          ifelse(fg_model$converged, "成功", "未完全收敛"),
          as.character(n_iter),
          as.character(length(selected_vars))),
  stringsAsFactors = FALSE
)

# ===============================================================================
# 8. 累积发病率函数分析
# ===============================================================================

cat("\n=== 第八步：累积发病率函数分析 ===\n")

cat("正在计算累积发病率函数...\n")

# 计算CIF
tryCatch({
  cif_result <- cuminc(
    ftime = model_data$time,              # 使用新的列名
    fstatus = model_data$vital,
    cencode = 0
  )

  cat("✓ 累积发病率函数计算成功\n")

  # 定义预测时间点（以10年为主要目标）
  primary_time_point <- 3650  # 10年 = 3650天（主要预测目标）
  primary_time_label <- "10年"

  # 补充时间点（用于模型评估和临床参考）
  supplementary_time_points <- c(365, 1095, 1825)  # 1, 3, 5年
  supplementary_labels <- c("1年", "3年", "5年")

  # 完整时间点列表
  time_points <- c(supplementary_time_points, primary_time_point)
  time_labels <- c(supplementary_labels, primary_time_label)

  cif_timepoints <- timepoints(cif_result, times = time_points)

  cat("\n--- 累积发病率分析（主要目标：10年预测）---\n")

  # 创建CIF结果表格
  cif_summary <- data.frame(
    时间点 = time_labels,
    时间_天 = time_points,
    主要事件CIF = round(cif_timepoints$est[1:4], 4),
    主要事件CIF_下限 = round(cif_timepoints$est[1:4] - 1.96*sqrt(cif_timepoints$var[1:4]), 4),
    主要事件CIF_上限 = round(cif_timepoints$est[1:4] + 1.96*sqrt(cif_timepoints$var[1:4]), 4),
    竞争事件CIF = round(cif_timepoints$est[5:8], 4),
    竞争事件CIF_下限 = round(cif_timepoints$est[5:8] - 1.96*sqrt(cif_timepoints$var[5:8]), 4),
    竞争事件CIF_上限 = round(cif_timepoints$est[5:8] + 1.96*sqrt(cif_timepoints$var[5:8]), 4),
    stringsAsFactors = FALSE
  )

  # 格式化CIF结果
  cif_summary$主要事件CIF_CI <- sprintf("%.4f (%.4f-%.4f)",
                                       cif_summary$主要事件CIF,
                                       cif_summary$主要事件CIF_下限,
                                       cif_summary$主要事件CIF_上限)

  cif_summary$竞争事件CIF_CI <- sprintf("%.4f (%.4f-%.4f)",
                                       cif_summary$竞争事件CIF,
                                       cif_summary$竞争事件CIF_下限,
                                       cif_summary$竞争事件CIF_上限)

  # 显示CIF结果（突出10年预测）
  cif_display <- cif_summary[, c("时间点", "主要事件CIF_CI", "竞争事件CIF_CI")]
  names(cif_display) <- c("时间点", "心血管死亡CIF (95%CI)", "其他死亡CIF (95%CI)")

  if (require(knitr, quietly = TRUE)) {
    print(kable(cif_display,
                caption = "累积发病率函数结果（主要预测目标：10年）",
                align = c("c", "c", "c")))
  } else {
    print(cif_display)
  }

  # 特别突出10年预测结果
  ten_year_index <- which(time_labels == "10年")
  if (length(ten_year_index) > 0) {
    cat("\n🎯 主要预测目标 - 10年心血管死亡风险:\n")
    cat("   10年心血管死亡CIF:", cif_display[ten_year_index, "心血管死亡CIF (95%CI)"], "\n")
    cat("   10年其他死亡CIF:", cif_display[ten_year_index, "其他死亡CIF (95%CI)"], "\n")
  }

}, error = function(e) {
  cat("累积发病率函数计算失败:", e$message, "\n")
  cif_result <- NULL
  cif_summary <- NULL
})

# 绘制累积发病率曲线并保存
cat("\n正在生成累积发病率曲线图...\n")
if (!is.null(cif_result)) {
  # 保存图形到文件
  png(file.path(output_dir, "累积发病率曲线.png"),
      width = 1200, height = 800, res = 150)

  plot(cif_result,
       main = "累积发病率函数 (Cumulative Incidence Function)",
       xlab = "时间 (天)",
       ylab = "累积发病率",
       col = c("red", "blue"),
       lty = c(1, 2),
       lwd = 2,
       xlim = c(0, max(model_data$time, na.rm = TRUE)))

  legend("topright",
         legend = c("主要事件", "竞争事件"),
         col = c("red", "blue"),
         lty = c(1, 2),
         lwd = 2)
  grid()

  dev.off()
  cat("✓ 累积发病率曲线图已保存\n")
}

# ===============================================================================
# 6. 个体化10年心血管死亡风险评分
# ===============================================================================

cat("\n计算个体化10年心血管死亡风险评分...\n")

# 计算线性预测值（对应10年心血管死亡风险）
linear_predictor <- as.vector(cov_matrix %*% fg_model$coef)
model_data$risk_score_10y <- linear_predictor  # 明确标注为10年风险评分

# ===============================================================================
# 风险分层方法学说明
# ===============================================================================
# 采用三分位数分层策略的理由：
# 1. 统计学合理性：确保各组样本量均衡，保证统计功效
# 2. 临床实用性：三分组便于临床理解和决策制定
# 3. 方法学标准：符合心血管风险预测研究的国际惯例
# 4. 预测性能：最大化风险区分能力，避免主观阈值选择偏倚

# 10年心血管死亡风险分层 (三分位数方法)
cat("正在进行风险分层分析...\n")
cat("分层方法: 三分位数分层 (33%和67%分位数)\n")
cat("分层依据: 基于Fine-Gray模型线性预测值\n")
cat("分层目标: 实现低危、中危、高危三组的均衡分布\n\n")

risk_quantiles <- quantile(linear_predictor, probs = c(0.33, 0.67))
cat("风险分层阈值:\n")
cat("  低危组: 风险评分 ≤", round(risk_quantiles[1], 3), "\n")
cat("  中危组: 风险评分", round(risk_quantiles[1], 3), "~", round(risk_quantiles[2], 3), "\n")
cat("  高危组: 风险评分 ≥", round(risk_quantiles[2], 3), "\n\n")

model_data$risk_group_10y <- cut(linear_predictor,
                                breaks = c(-Inf, risk_quantiles[1], risk_quantiles[2], Inf),
                                labels = c("低危", "中危", "高危"))

# 10年心血管死亡风险分层统计
risk_summary <- model_data %>%
  group_by(risk_group_10y) %>%
  summarise(
    n = n(),
    events = sum(vital == 1),
    event_rate = round(events/n * 100, 2),
    mean_score = round(mean(risk_score_10y), 3),
    median_score = round(median(risk_score_10y), 3),
    .groups = 'drop'
  )

# ===============================================================================
# 风险分层效果评估
# ===============================================================================
cat("=== 风险分层效果评估 ===\n")

# 计算风险分层的区分能力
high_risk_rate <- risk_summary$event_rate[risk_summary$risk_group_10y == "高危"]
low_risk_rate <- risk_summary$event_rate[risk_summary$risk_group_10y == "低危"]
risk_ratio <- high_risk_rate / low_risk_rate

cat("风险分层区分能力:\n")
cat("  高危组事件率:", high_risk_rate, "%\n")
cat("  低危组事件率:", low_risk_rate, "%\n")
cat("  风险比 (高危/低危):", round(risk_ratio, 1), "倍\n")

# 评估分层质量
if (risk_ratio >= 10) {
  stratification_quality <- "优秀"
  cat("✓ 风险分层效果优秀 (风险比≥10倍)\n")
} else if (risk_ratio >= 5) {
  stratification_quality <- "良好"
  cat("✓ 风险分层效果良好 (风险比≥5倍)\n")
} else if (risk_ratio >= 3) {
  stratification_quality <- "中等"
  cat("⚠️ 风险分层效果中等 (风险比≥3倍)\n")
} else {
  stratification_quality <- "较差"
  cat("⚠️ 风险分层效果较差 (风险比<3倍)\n")
}

cat("\n10年心血管死亡风险分层结果:\n")
risk_display <- risk_summary
names(risk_display) <- c("风险分组", "样本量", "事件数", "事件率(%)", "平均评分", "中位评分")

# 美化显示10年风险分层结果
if (require(knitr, quietly = TRUE)) {
  print(kable(risk_display,
              caption = "基于三分位数的10年心血管死亡风险分层结果",
              align = c("l", "c", "c", "c", "c", "c")))
} else {
  print(risk_display)
}

# ===============================================================================
# 风险分层的临床意义评估
# ===============================================================================
cat("\n=== 风险分层的临床意义 ===\n")

# 计算并显示10年CIF风险评分
if (exists("cif_timepoints") && length(ten_year_index) > 0) {
  ten_year_cif <- cif_timepoints$est[ten_year_index]
  cat("整体10年心血管死亡CIF:", sprintf("%.4f (%.2f%%)", ten_year_cif, ten_year_cif*100), "\n")
}

# 风险分层的临床解释
cat("\n风险分层的临床应用价值:\n")
cat("1. 低危组 (", risk_summary$n[1], "例):\n")
cat("   - 事件率:", low_risk_rate, "% (", risk_summary$events[1], "/", risk_summary$n[1], ")\n")
cat("   - 临床建议: 常规随访，生活方式干预\n")

cat("2. 中危组 (", risk_summary$n[2], "例):\n")
cat("   - 事件率:", risk_summary$event_rate[2], "% (", risk_summary$events[2], "/", risk_summary$n[2], ")\n")
cat("   - 临床建议: 加强监测，药物干预考虑\n")

cat("3. 高危组 (", risk_summary$n[3], "例):\n")
cat("   - 事件率:", high_risk_rate, "% (", risk_summary$events[3], "/", risk_summary$n[3], ")\n")
cat("   - 临床建议: 积极干预，密切随访\n")

cat("\n风险分层方法学验证:\n")
cat("✓ 三分位数分层确保样本量均衡 (", min(risk_summary$n), "-", max(risk_summary$n), "例)\n")
cat("✓ 风险梯度明显 (", low_risk_rate, "% → ", risk_summary$event_rate[2], "% → ", high_risk_rate, "%)\n")
cat("✓ 高低危组风险比达", round(risk_ratio, 1), "倍，区分度", stratification_quality, "\n")
cat("✓ 符合心血管风险预测研究的国际标准\n")

# ===============================================================================
# 风险分层可视化 (论文级别图表)
# ===============================================================================
cat("\n=== 生成风险分层可视化图表 ===\n")

if (require(ggplot2, quietly = TRUE)) {
  # 1. 风险分层事件率柱状图 (论文主图)
  p1 <- ggplot(risk_summary, aes(x = risk_group_10y, y = event_rate, fill = risk_group_10y)) +
    geom_col(alpha = 0.8, width = 0.6) +
    geom_text(aes(label = paste0(event_rate, "%\n(", events, "/", n, ")")),
              vjust = -0.3, size = 4, fontface = "bold") +
    labs(title = "基于三分位数的10年心血管死亡风险分层效果",
         subtitle = paste0("风险比 (高危/低危) = ", round(risk_ratio, 1), "倍"),
         x = "风险分组",
         y = "10年心血管死亡事件率 (%)") +
    scale_fill_manual(values = c("低危" = "#2E8B57", "中危" = "#FF8C00", "高危" = "#DC143C"),
                      name = "风险分组") +
    scale_y_continuous(limits = c(0, max(risk_summary$event_rate) * 1.2),
                       breaks = seq(0, ceiling(max(risk_summary$event_rate)), by = 5)) +
    theme_minimal() +
    theme(
      plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
      plot.subtitle = element_text(hjust = 0.5, size = 12),
      axis.title = element_text(size = 12),
      axis.text = element_text(size = 11),
      legend.position = "none",
      panel.grid.minor = element_blank()
    )

  print(p1)

  # 保存高质量风险分层图
  ggsave(file.path(output_dir, "风险分层事件率图.png"), p1,
         width = 10, height = 7, dpi = 300, bg = "white")
  cat("✓ 论文级风险分层事件率图已保存\n")

  # 绘制10年风险评分分布图
  p2 <- ggplot(model_data, aes(x = risk_score_10y, fill = risk_group_10y)) +
    geom_histogram(bins = 30, alpha = 0.7) +
    facet_wrap(~risk_group_10y, scales = "free_y") +
    labs(title = "10年心血管死亡风险评分分布",
         x = "10年风险评分",
         y = "频数") +
    scale_fill_manual(values = c("低危" = "green", "中危" = "orange", "高危" = "red")) +
    theme_minimal() +
    theme(legend.position = "none")

  print(p2)

  # 保存风险评分分布图
  ggsave(file.path(output_dir, "风险评分分布图.png"), p2,
         width = 12, height = 8, dpi = 300)
  cat("✓ 风险评分分布图已保存\n")
} else {
  # 如果没有ggplot2，使用基础绘图
  barplot(risk_summary$event_rate,
          names.arg = risk_summary$risk_group_10y,
          main = "10年心血管死亡风险分组事件率",
          xlab = "10年风险分组",
          ylab = "事件率 (%)",
          col = c("green", "orange", "red"))
}

# ===============================================================================
# 7. 综合结果展示
# ===============================================================================

cat("\n=== 综合分析结果展示 ===\n")

# 显示累积发病率表格
cif_df <- data.frame(
  Time_Years = c(1, 3, 5, 10),
  Time_Days = time_points,
  CV_Death_CIF = round(cif_timepoints$est[1:4], 4),
  Other_Death_CIF = round(cif_timepoints$est[5:8], 4)
)
names(cif_df) <- c("时间(年)", "时间(天)", "心血管死亡CIF", "其他死亡CIF")

cat("\n累积发病率函数结果:\n")
if (require(knitr, quietly = TRUE)) {
  print(kable(cif_df,
              caption = "不同时间点的累积发病率",
              align = c("c", "c", "c", "c")))
} else {
  print(cif_df)
}

# 显示详细的模型结果表格
cat("\n详细模型结果:\n")

# 定义详细结果需要的列
detailed_cols <- c("Variable", "Coefficient", "SE", "sHR", "sHR_lower", "sHR_upper", "P_value")
detailed_names <- c("变量", "系数", "标准误", "sHR", "sHR下限", "sHR上限", "P值")

# 只选择存在的列
existing_detailed_cols <- detailed_cols[detailed_cols %in% names(results_table)]
corresponding_detailed_names <- detailed_names[1:length(existing_detailed_cols)]

detailed_results <- results_table[, existing_detailed_cols]
names(detailed_results) <- corresponding_detailed_names

# 格式化P值
if ("P值" %in% names(detailed_results)) {
  detailed_results$P值 <- round(detailed_results$P值, 4)
}

if (require(knitr, quietly = TRUE)) {
  print(kable(detailed_results,
              caption = "Fine-Gray模型详细结果",
              digits = 4,
              align = c("l", rep("c", 6))))
} else {
  print(detailed_results)
}

# 生成文字总结报告
cat("\n=== 分析总结报告 ===\n")
cat("分析日期:", as.character(Sys.Date()), "\n")
cat("样本量:", n_total, "\n")
cat("主要事件数 (心血管死亡):", n_events, "\n")
cat("竞争事件数 (其他死亡):", n_competing, "\n")
cat("删失数:", n_censored, "\n")
cat("主要事件率:", round(n_events/n_total * 100, 2), "%\n")
cat("EPV比例:", round(epv, 1), "\n")
cat("模型收敛状态:", ifelse(fg_model$converged, "成功收敛", "未收敛"), "\n")

cat("\n主要发现:\n")
for (i in 1:nrow(results_table)) {
  significance <- ifelse(results_table$P_value[i] < 0.05, " (统计学显著)", " (无统计学意义)")
  cat("-", results_table$Variable[i], ": sHR =", results_table$sHR_CI[i],
      ", P =", results_table$P_value_formatted[i], significance, "\n")
}

cat("\n10年心血管死亡风险分层效果:\n")
for (i in 1:nrow(risk_summary)) {
  cat("-", risk_summary$risk_group_10y[i], "组:", risk_summary$n[i], "例,",
      "事件率", risk_summary$event_rate[i], "%\n")
}

# ===============================================================================
# 8. 额外可视化分析
# ===============================================================================

cat("\n=== 额外可视化分析 ===\n")

# 绘制变量重要性图
if (require(ggplot2, quietly = TRUE)) {
  # 创建变量重要性数据
  var_importance <- data.frame(
    Variable = results_table$Variable,
    Abs_Coefficient = abs(results_table$Coefficient),
    Significance = ifelse(results_table$P_value < 0.05, "显著", "不显著")
  )

  # 变量重要性图
  p3 <- ggplot(var_importance, aes(x = reorder(Variable, Abs_Coefficient),
                                   y = Abs_Coefficient,
                                   fill = Significance)) +
    geom_col(alpha = 0.7) +
    coord_flip() +
    labs(title = "变量重要性 (基于系数绝对值)",
         x = "变量",
         y = "系数绝对值") +
    scale_fill_manual(values = c("显著" = "red", "不显著" = "gray")) +
    theme_minimal()

  print(p3)

  # 保存变量重要性图
  ggsave(file.path(output_dir, "变量重要性图.png"), p3,
         width = 10, height = 6, dpi = 300)
  cat("✓ 变量重要性图已保存\n")

  # 森林图风格的系数图
  p4 <- ggplot(results_table, aes(x = Variable, y = sHR)) +
    geom_point(size = 3) +
    geom_errorbar(aes(ymin = sHR_lower, ymax = sHR_upper), width = 0.2) +
    geom_hline(yintercept = 1, linetype = "dashed", color = "red") +
    coord_flip() +
    labs(title = "亚分布风险比森林图",
         x = "变量",
         y = "亚分布风险比 (sHR)") +
    theme_minimal()

  print(p4)

  # 保存森林图
  ggsave(file.path(output_dir, "亚分布风险比森林图.png"), p4,
         width = 10, height = 6, dpi = 300)
  cat("✓ 亚分布风险比森林图已保存\n")
}

# 显示模型预测性能总结
cat("\n=== 模型性能总结 ===\n")
cat("1. 统计学显著的变量数:", sum(results_table$P_value < 0.05), "/", nrow(results_table), "\n")
cat("2. 风险增加的变量数:", sum(results_table$sHR > 1), "/", nrow(results_table), "\n")
cat("3. 风险降低的变量数:", sum(results_table$sHR < 1), "/", nrow(results_table), "\n")
cat("4. 高危组与低危组事件率比:",
    round(risk_summary$event_rate[3] / risk_summary$event_rate[1], 2), "\n")

# 显示关键临床发现
cat("\n=== 关键临床发现 ===\n")
significant_vars <- results_table[results_table$P_value < 0.05, ]
if (nrow(significant_vars) > 0) {
  cat("统计学显著的危险因素:\n")
  for (i in 1:nrow(significant_vars)) {
    direction <- ifelse(significant_vars$sHR[i] > 1, "增加", "降低")
    cat("-", significant_vars$Variable[i], ":", direction, "心血管死亡风险",
        round((abs(significant_vars$sHR[i] - 1)) * 100, 1), "%\n")
  }
} else {
  cat("未发现统计学显著的危险因素\n")
}

# ===============================================================================
# 12. 保存完整结果
# ===============================================================================

cat("\n=== 第十二步：保存完整结果 ===\n")

# 创建Excel工作簿
wb <- createWorkbook()

# 工作表1：模型主要结果
addWorksheet(wb, "模型主要结果")

# 定义要保存的列和对应的中文名称
save_cols_basic <- c("Variable", "Coefficient", "SE", "sHR", "sHR_lower", "sHR_upper", "P_value", "Significance")
save_names_basic <- c("变量", "系数", "标准误", "sHR", "sHR下限", "sHR上限", "P值", "显著性")

# 如果有额外的列，添加它们
if ("Clinical_Name" %in% names(results_table)) {
  save_cols <- c("Variable", "Clinical_Name", "Clinical_Category", "Variable_Type",
                 "Coefficient", "SE", "sHR", "sHR_lower", "sHR_upper", "P_value", "Significance")
  save_names <- c("变量", "临床名称", "变量分类", "变量类型", "系数", "标准误", "sHR", "sHR下限", "sHR上限", "P值", "显著性")
} else if ("Variable_Type" %in% names(results_table)) {
  save_cols <- c("Variable", "Variable_Type", "Coefficient", "SE", "sHR", "sHR_lower", "sHR_upper", "P_value", "Significance")
  save_names <- c("变量", "变量类型", "系数", "标准误", "sHR", "sHR下限", "sHR上限", "P值", "显著性")
} else {
  save_cols <- save_cols_basic
  save_names <- save_names_basic
}

# 只选择存在的列
existing_save_cols <- save_cols[save_cols %in% names(results_table)]
corresponding_save_names <- save_names[1:length(existing_save_cols)]

model_results_save <- results_table[, existing_save_cols]
names(model_results_save) <- corresponding_save_names
writeData(wb, "模型主要结果", model_results_save)
cat("✓ 工作表1：模型主要结果\n")

# 工作表2：模型诊断
addWorksheet(wb, "模型诊断")
writeData(wb, "模型诊断", model_diagnostics)
cat("✓ 工作表2：模型诊断\n")

# 工作表3：累积发病率
if (!is.null(cif_summary)) {
  addWorksheet(wb, "累积发病率")
  cif_save <- cif_summary[, c("时间点", "时间_天", "主要事件CIF", "主要事件CIF_下限", "主要事件CIF_上限",
                              "竞争事件CIF", "竞争事件CIF_下限", "竞争事件CIF_上限")]
  writeData(wb, "累积发病率", cif_save)
  cat("✓ 工作表3：累积发病率\n")
}

# 工作表4：风险分层结果
if (exists("risk_summary")) {
  addWorksheet(wb, "风险分层结果")
  writeData(wb, "风险分层结果", risk_summary)
  cat("✓ 工作表4：风险分层结果\n")
}

# 工作表5：分析摘要
analysis_summary <- data.frame(
  项目 = c("分析日期", "分析时间", "输入文件", "输出目录", "建模方法",
          "样本量", "变量数", "主要事件数", "竞争事件数", "删失数",
          "EPV比例", "模型收敛", "统计显著变量数"),
  结果 = c(as.character(Sys.Date()),
          format(Sys.time(), "%H:%M:%S"),
          basename(input_file),
          output_dir,
          "Fine-Gray竞争风险模型",
          as.character(n_total),
          as.character(length(selected_vars)),
          as.character(n_events),
          as.character(n_competing),
          as.character(n_censored),
          paste0(round(epv, 1), ":1"),
          ifelse(fg_model$converged, "成功", "未完全收敛"),
          as.character(nrow(significant_vars))),
  备注 = c("脚本执行日期", "脚本执行时间", "LASSO筛选的建模数据集", "结果保存目录",
          "使用cmprsk包的crr函数", "完整案例数", "LASSO筛选的核心变量",
          "主要终点事件", "竞争终点事件", "随访期间未发生事件",
          "事件数与变量数比例", "模型收敛状态", "P<0.05的变量数"),
  stringsAsFactors = FALSE
)

addWorksheet(wb, "分析摘要")
writeData(wb, "分析摘要", analysis_summary)
cat("✓ 工作表5：分析摘要\n")

# 保存Excel文件
tryCatch({
  saveWorkbook(wb, fg_output_file, overwrite = TRUE)
  cat("✓ Excel结果文件保存成功:", basename(fg_output_file), "\n")
}, error = function(e) {
  cat("Excel文件保存失败:", e$message, "\n")
})

# 保存完整的R对象结果
fg_complete_results <- list(
  model = fg_model,
  results_table = results_table,
  model_diagnostics = model_diagnostics,
  cif_result = if(exists("cif_result")) cif_result else NULL,
  cif_summary = if(exists("cif_summary")) cif_summary else NULL,
  risk_summary = if(exists("risk_summary")) risk_summary else NULL,
  model_data = model_data,
  selected_vars = selected_vars,
  variable_types = list(
    binary_vars = binary_vars,
    continuous_vars = continuous_vars,
    categorical_vars = categorical_vars
  ),
  analysis_params = list(
    input_file = input_file,
    output_dir = output_dir,
    sample_size = n_total,
    n_events = n_events,
    n_competing = n_competing,
    n_censored = n_censored,
    epv_ratio = epv,
    converged = fg_model$converged,
    n_iterations = n_iter,
    analysis_date = Sys.Date(),
    analysis_time = Sys.time()
  )
)

tryCatch({
  saveRDS(fg_complete_results, fg_results_file)
  cat("✓ 完整结果对象保存成功:", basename(fg_results_file), "\n")
}, error = function(e) {
  cat("RDS文件保存失败:", e$message, "\n")
})

# ===============================================================================
# 13. 生成总结报告
# ===============================================================================

cat("\n", rep("=", 80), "\n")
cat("                    Fine-Gray 10年心血管死亡风险建模完成总结                \n")
cat(rep("=", 80), "\n")

cat("分析时间:", format(Sys.time(), "%Y-%m-%d %H:%M:%S"), "\n")
cat("输入文件:", input_file, "\n")
cat("输出目录:", output_dir, "\n")

cat("\n--- 数据基本信息 ---\n")
cat("样本量:", n_total, "个\n")
cat("变量数:", length(selected_vars), "个\n")
cat("变量列表:", paste(selected_vars, collapse = ", "), "\n")

cat("\n--- 事件分布 ---\n")
cat("主要事件:", n_events, "例 (", round(n_events/n_total * 100, 1), "%)\n")
cat("竞争事件:", n_competing, "例 (", round(n_competing/n_total * 100, 1), "%)\n")
cat("删失事件:", n_censored, "例 (", round(n_censored/n_total * 100, 1), "%)\n")

cat("\n--- 模型质量 ---\n")
cat("EPV比例:", round(epv, 1), ":1 (", epv_quality, ")\n")
cat("模型收敛:", ifelse(fg_model$converged, "✓ 成功", "⚠️ 未完全收敛"), "\n")
cat("迭代次数:", n_iter, "\n")

cat("\n--- 10年心血管死亡风险预测结果 ---\n")
cat("统计显著变量数 (P<0.05):", nrow(significant_vars), "/", nrow(results_table), "\n")
if (nrow(significant_vars) > 0) {
  cat("显著变量:", paste(significant_vars$Variable, collapse = ", "), "\n")

  cat("\n主要发现（10年心血管死亡风险）:\n")
  for (i in 1:nrow(significant_vars)) {
    direction <- ifelse(significant_vars$sHR[i] > 1, "增加", "降低")
    magnitude <- round(abs(significant_vars$sHR[i] - 1) * 100, 1)
    cat(sprintf("- %s: %s10年心血管死亡风险%.1f%% (sHR=%.3f, P=%.3f)\n",
                significant_vars$Variable[i], direction, magnitude,
                significant_vars$sHR[i], significant_vars$P_value[i]))
  }
} else {
  cat("未发现统计显著的10年心血管死亡危险因素\n")
}

cat("\n--- 输出文件 ---\n")
cat("1. Excel结果文件:", basename(fg_output_file), "\n")
cat("2. 完整结果对象:", basename(fg_results_file), "\n")
cat("3. 累积发病率曲线图: 累积发病率曲线.png\n")
cat("4. 风险分层事件率图: 风险分层事件率图.png\n")
cat("5. 风险评分分布图: 风险评分分布图.png\n")
cat("6. 变量重要性图: 变量重要性图.png\n")
cat("7. 亚分布风险比森林图: 亚分布风险比森林图.png\n")
cat("8. 10年心血管死亡风险预测模型已构建完成\n")

cat("\n--- 模型质量综合评估 ---\n")

# 计算模型质量评分
quality_score <- 0
quality_details <- c()

# 1. 收敛性评估 (20分)
if (fg_model$converged) {
  quality_score <- quality_score + 20
  quality_details <- c(quality_details, "✓ 模型收敛 (+20分)")
} else {
  quality_score <- quality_score + 10
  quality_details <- c(quality_details, "⚠️ 模型未完全收敛 (+10分)")
}

# 2. EPV比例评估 (30分)
if (epv >= 20) {
  quality_score <- quality_score + 30
  quality_details <- c(quality_details, "✓ EPV≥20，统计功效优秀 (+30分)")
} else if (epv >= 10) {
  quality_score <- quality_score + 25
  quality_details <- c(quality_details, "✓ EPV≥10，统计功效良好 (+25分)")
} else if (epv >= 5) {
  quality_score <- quality_score + 15
  quality_details <- c(quality_details, "⚠️ EPV≥5，统计功效一般 (+15分)")
} else {
  quality_score <- quality_score + 5
  quality_details <- c(quality_details, "⚠️ EPV<5，过拟合高风险 (+5分)")
}

# 3. 统计显著性评估 (25分)
sig_ratio <- nrow(significant_vars) / nrow(results_table)
if (sig_ratio >= 0.5) {
  quality_score <- quality_score + 25
  quality_details <- c(quality_details, sprintf("✓ %.0f%%变量显著，预测能力强 (+25分)", sig_ratio*100))
} else if (sig_ratio >= 0.25) {
  quality_score <- quality_score + 20
  quality_details <- c(quality_details, sprintf("✓ %.0f%%变量显著，预测能力中等 (+20分)", sig_ratio*100))
} else if (sig_ratio > 0) {
  quality_score <- quality_score + 10
  quality_details <- c(quality_details, sprintf("⚠️ %.0f%%变量显著，预测能力较弱 (+10分)", sig_ratio*100))
} else {
  quality_score <- quality_score + 0
  quality_details <- c(quality_details, "⚠️ 无显著变量，预测能力不足 (+0分)")
}

# 4. 样本量评估 (15分)
if (n_total >= 2000) {
  quality_score <- quality_score + 15
  quality_details <- c(quality_details, "✓ 样本量充足 (+15分)")
} else if (n_total >= 1000) {
  quality_score <- quality_score + 12
  quality_details <- c(quality_details, "✓ 样本量良好 (+12分)")
} else if (n_total >= 500) {
  quality_score <- quality_score + 8
  quality_details <- c(quality_details, "⚠️ 样本量一般 (+8分)")
} else {
  quality_score <- quality_score + 3
  quality_details <- c(quality_details, "⚠️ 样本量偏小 (+3分)")
}

# 5. 事件率评估 (10分)
event_rate <- n_events / n_total
if (event_rate >= 0.1) {
  quality_score <- quality_score + 10
  quality_details <- c(quality_details, "✓ 事件率充足 (+10分)")
} else if (event_rate >= 0.05) {
  quality_score <- quality_score + 7
  quality_details <- c(quality_details, "✓ 事件率良好 (+7分)")
} else {
  quality_score <- quality_score + 3
  quality_details <- c(quality_details, "⚠️ 事件率偏低 (+3分)")
}

# 显示质量评估结果
cat("🏆 模型质量评分:", quality_score, "/100分\n")

if (quality_score >= 85) {
  cat("📊 质量等级: A+ (优秀) - 模型质量极高，可直接用于临床应用\n")
} else if (quality_score >= 75) {
  cat("📊 质量等级: A (良好) - 模型质量很好，建议进行验证后应用\n")
} else if (quality_score >= 65) {
  cat("📊 质量等级: B (中等) - 模型质量尚可，需要改进后应用\n")
} else if (quality_score >= 50) {
  cat("📊 质量等级: C (一般) - 模型质量一般，需要显著改进\n")
} else {
  cat("📊 质量等级: D (较差) - 模型质量不足，不建议直接应用\n")
}

cat("\n详细评分:\n")
for (detail in quality_details) {
  cat("  ", detail, "\n")
}

cat("\n--- 下一步建议 ---\n")
cat("📊 模型验证建议:\n")
cat("1. 在独立验证集上评估模型性能 (C-index, 校准度)\n")
cat("2. 进行Bootstrap内部验证评估模型稳定性\n")
cat("3. 计算模型的判别能力指标 (AUC, NRI, IDI)\n")
cat("4. 进行校准图分析评估预测准确性\n")

cat("\n🔬 统计学验证建议:\n")
cat("1. 检查比例风险假设 (Schoenfeld残差)\n")
cat("2. 进行敏感性分析 (排除极端值)\n")
cat("3. 评估模型的拟合优度\n")
cat("4. 进行多重插补处理缺失值 (如有)\n")

cat("\n🏥 临床应用建议:\n")
cat("1. 开发基于Web的风险计算器\n")
cat("2. 制定风险分层的临床管理策略\n")
cat("3. 在多中心队列中进行外部验证\n")
cat("4. 评估模型的成本效益分析\n")
cat("5. 制定临床实践指南和决策支持工具\n")

cat("\n📈 模型改进建议:\n")
if (nrow(significant_vars) < 2) {
  cat("1. 考虑增加样本量以提高统计功效\n")
  cat("2. 延长随访时间以获得更多事件\n")
  cat("3. 考虑添加其他重要的临床变量\n")
}
if (epv < 10) {
  cat("1. EPV比例偏低，建议增加样本量或减少变量数\n")
  cat("2. 考虑使用正则化方法 (如已使用LASSO)\n")
}
cat("1. 考虑非线性关系和交互作用\n")
cat("2. 评估时间依赖性协变量的影响\n")
cat("3. 考虑竞争风险的异质性\n")

cat("\n", rep("=", 80), "\n")
cat("✅ Fine-Gray 10年心血管死亡风险建模脚本执行完成！\n")
cat("📁 所有结果文件已保存到:", output_dir, "\n")
cat("🎯 10年心血管死亡风险预测模型构建完成，可进行临床应用\n")
cat(rep("=", 80), "\n")
